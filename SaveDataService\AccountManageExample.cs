using System;
using SaveDataService;

namespace SaveDataService
{
    /// <summary>
    /// AccountManage 使用示例
    /// </summary>
    public class AccountManageExample
    {
        /// <summary>
        /// 演示注册功能
        /// </summary>
        public static void DemoRegister()
        {
            Console.WriteLine("=== 注册功能演示 ===");
            
            // 用户名注册
            var registerResult1 = AccountManage.RegisterByUsername("testuser", "password123", "<EMAIL>", "***********");
            Console.WriteLine($"用户名注册: {registerResult1.Success} - {registerResult1.Message}");
            if (registerResult1.Success)
            {
                Console.WriteLine($"账号ID: {registerResult1.AccountId}");
                Console.WriteLine($"访问令牌: {registerResult1.AccessToken}");
            }
            
            // 邮箱注册
            var registerResult2 = AccountManage.RegisterByEmail("<EMAIL>", "password456");
            Console.WriteLine($"邮箱注册: {registerResult2.Success} - {registerResult2.Message}");
            
            // 手机号注册
            var registerResult3 = AccountManage.RegisterByMobile("***********", "password789");
            Console.WriteLine($"手机号注册: {registerResult3.Success} - {registerResult3.Message}");
            
            Console.WriteLine();
        }
        
        /// <summary>
        /// 演示登录功能
        /// </summary>
        public static void DemoLogin()
        {
            Console.WriteLine("=== 登录功能演示 ===");
            
            // 用户名登录
            var loginResult1 = AccountManage.LoginByUsername("testuser", "password123", "192.168.1.100");
            Console.WriteLine($"用户名登录: {loginResult1.Success} - {loginResult1.Message}");
            if (loginResult1.Success && loginResult1.Account != null)
            {
                Console.WriteLine($"用户昵称: {loginResult1.Account.nickname}");
                Console.WriteLine($"访问令牌: {loginResult1.AccessToken}");
            }
            
            // 邮箱登录
            var loginResult2 = AccountManage.LoginByEmail("<EMAIL>", "password123", "192.168.1.101");
            Console.WriteLine($"邮箱登录: {loginResult2.Success} - {loginResult2.Message}");
            
            // 手机号登录
            var loginResult3 = AccountManage.LoginByMobile("***********", "password123", "192.168.1.102");
            Console.WriteLine($"手机号登录: {loginResult3.Success} - {loginResult3.Message}");
            
            // 令牌登录
            if (loginResult1.Success)
            {
                var tokenLoginResult = AccountManage.LoginByToken(loginResult1.AccessToken);
                Console.WriteLine($"令牌登录: {tokenLoginResult.Success} - {tokenLoginResult.Message}");
            }
            
            Console.WriteLine();
        }
        
        /// <summary>
        /// 演示验证码功能
        /// </summary>
        public static void DemoVerificationCode()
        {
            Console.WriteLine("=== 验证码功能演示 ===");

            // 发送邮箱验证码
            var sendEmailResult = AccountManage.SendEmailVerificationCode("<EMAIL>");
            Console.WriteLine($"发送邮箱验证码: {sendEmailResult.Success} - {sendEmailResult.Message}");

            // 发送手机验证码
            var sendMobileResult = AccountManage.SendMobileVerificationCode("***********");
            Console.WriteLine($"发送手机验证码: {sendMobileResult.Success} - {sendMobileResult.Message}");

            Console.WriteLine();
        }

        /// <summary>
        /// 演示密码重置功能
        /// </summary>
        public static void DemoResetPassword()
        {
            Console.WriteLine("=== 密码重置功能演示 ===");

            // 通过邮箱验证码重置密码（需要先发送验证码）
            var resetResult1 = AccountManage.ResetPasswordByEmail("<EMAIL>", "123456", "newpassword123");
            Console.WriteLine($"邮箱验证码重置密码: {resetResult1.Success} - {resetResult1.Message}");

            // 通过手机验证码重置密码（需要先发送验证码）
            var resetResult2 = AccountManage.ResetPasswordByMobile("***********", "123456", "newpassword456");
            Console.WriteLine($"手机验证码重置密码: {resetResult2.Success} - {resetResult2.Message}");

            // 获取安全问题
            var getQuestionsResult = AccountManage.GetSecurityQuestions("testuser");
            Console.WriteLine($"获取安全问题: {getQuestionsResult.Success} - {getQuestionsResult.Message}");
            if (getQuestionsResult.Success && getQuestionsResult.SecurityQuestions != null)
            {
                Console.WriteLine("安全问题:");
                for (int i = 0; i < getQuestionsResult.SecurityQuestions.Length; i++)
                {
                    Console.WriteLine($"  {i + 1}. {getQuestionsResult.SecurityQuestions[i]}");
                }
            }

            // 通过安全问题重置密码
            var securityAnswers = new string[] { "北京", "张三", "红色" };
            var resetResult3 = AccountManage.ResetPasswordBySecurityQuestion("testuser", securityAnswers, "newpassword789");
            Console.WriteLine($"安全问题重置密码: {resetResult3.Success} - {resetResult3.Message}");

            Console.WriteLine();
        }
        
        /// <summary>
        /// 演示账号管理功能
        /// </summary>
        public static void DemoAccountManagement()
        {
            Console.WriteLine("=== 账号管理功能演示 ===");
            
            // 先登录获取账号ID
            var loginResult = AccountManage.LoginByUsername("testuser", "newpassword123");
            if (!loginResult.Success || loginResult.Account == null)
            {
                Console.WriteLine("需要先登录才能演示账号管理功能");
                return;
            }

            var accountId = loginResult.Account.id;
            
            // 修改密码
            var changePasswordResult = AccountManage.ChangePassword(accountId, "newpassword123", "finalpassword123");
            Console.WriteLine($"修改密码: {changePasswordResult.Success} - {changePasswordResult.Message}");
            
            // 设置安全问题
            var securityQuestions = new string[] { "您出生的城市是？", "您的小学老师姓名？", "您最喜欢的颜色？" };
            var securityAnswers = new string[] { "北京", "张三", "红色" };
            var setSecurityResult = AccountManage.SetSecurityQuestions(accountId, securityQuestions, securityAnswers);
            Console.WriteLine($"设置安全问题: {setSecurityResult.Success} - {setSecurityResult.Message}");
            
            // 启用二次验证
            var twoFactorResult = AccountManage.SetTwoFactorAuth(accountId, true);
            Console.WriteLine($"启用二次验证: {twoFactorResult.Success} - {twoFactorResult.Message}");
            
            // 更新账号信息
            var updateResult = AccountManage.UpdateAccountInfo(accountId, "新昵称", "avatar.jpg", "张三");
            Console.WriteLine($"更新账号信息: {updateResult.Success} - {updateResult.Message}");
            
            Console.WriteLine();
        }
        
        /// <summary>
        /// 运行所有演示
        /// </summary>
        public static void RunAllDemos()
        {
            try
            {
                Console.WriteLine("AccountManage 功能演示开始");
                Console.WriteLine("================================");
                
                DemoRegister();
                DemoLogin();
                DemoVerificationCode();
                DemoResetPassword();
                DemoAccountManagement();
                
                Console.WriteLine("================================");
                Console.WriteLine("AccountManage 功能演示结束");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"演示过程中发生错误: {ex.Message}");
                Console.WriteLine($"错误详情: {ex.StackTrace}");
            }
        }
    }
}
